import axiosInstance from '../config/axios';

export async function getStudentProfileData() {
  const response = await axiosInstance.get('/student-profile/all-data');
  return response.data;
}

export async function updateStudentProfileData(data: any) {
  const response = await axiosInstance.put('/student-profile/combined', data);
  return response.data;
}

export async function getClassroomOptions() {
  const response = await axiosInstance.get('/student-profile/classroom-options');
  return response.data;
}

export async function getConstantsByCategory(category: string) {
  const response = await axiosInstance.get(`/constant/${category}`);
  return response.data;
}