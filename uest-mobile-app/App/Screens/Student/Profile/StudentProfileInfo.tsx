import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { RootState } from '../../../Redux/store';
import { PrimaryColors, IMAGE_CONSTANT } from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import { getStudentProfileData } from '../../../services/studentProfileService';
import { getCurrentStudent } from '../../../services/studentService';
import { imgBaseUrl } from '../../../config/apiUrl';

interface StudentData {
  firstName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  middleName?: string;
  mothersName?: string;
  profilePhoto?: string;
}

interface ProfileData {
  medium?: string;
  classroom?: string;
  birthday?: string;
  school?: string;
  address?: string;
  gender?: string;
  age?: number;
  aadhaarNo?: string;
  bloodGroup?: string;
  birthPlace?: string;
  motherTongue?: string;
  religion?: string;
  caste?: string;
  subCaste?: string;
  photo?: string;
}

const StudentProfileInfo = () => {
  const navigation = useNavigation<any>();
  const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);
  
  const [studentData, setStudentData] = useState<StudentData>({});
  const [profileData, setProfileData] = useState<ProfileData>({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('Personal');

  useEffect(() => {
    fetchStudentData();
  }, []);

  const fetchStudentData = async () => {
    try {
      setLoading(true);
      
      // Fetch basic student info
      const studentRes = await getCurrentStudent();
      setStudentData(studentRes.data || {});
      
      // Fetch profile info
      const profileRes = await getStudentProfileData();
      setProfileData(profileRes.data?.profile || {});
      
    } catch (error) {
      console.error('Error fetching student data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not provided';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    } catch {
      return 'Invalid date';
    }
  };

  const renderAvatar = () => {
    const initials = `${studentData.firstName?.charAt(0) || 'U'}${studentData.lastName?.charAt(0) || 'S'}`;
    
    return (
      <View style={styles.avatarContainer}>
        <View style={[styles.avatar, styles.defaultAvatar]}>
          {profileData.photo ? (
            <Image
              source={{ uri: `${imgBaseUrl}/${profileData.photo}` }}
              style={styles.avatarImage}
              resizeMode="cover"
              onError={() => console.log('Error loading profile image')}
            />
          ) : (
            <Text style={styles.avatarText}>{initials.toUpperCase()}</Text>
          )}
        </View>
      </View>
    );
  };

  const renderPersonalInfo = () => (
    <View style={[styles.section, { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }]}>
      <Text style={[styles.sectionTitle, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
        Personal Information
      </Text>
      
      <View style={styles.infoGrid}>
        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            First Name
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {studentData.firstName || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Last Name
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {studentData.lastName || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Middle Name
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {studentData.middleName || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Mother's Name
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {studentData.mothersName || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Email
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {studentData.email || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Contact
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {studentData.contact || 'Not provided'}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderOtherInfo = () => (
    <View style={[styles.section, { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }]}>
      <Text style={[styles.sectionTitle, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
        Other Information
      </Text>
      
      <View style={styles.infoGrid}>
        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Medium
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.medium || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Classroom
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.classroom || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Birthday
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {formatDate(profileData.birthday)}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            School
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.school || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Address
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.address || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Gender
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.gender || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Age
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.age ? `${profileData.age} years` : 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Aadhaar Number
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.aadhaarNo || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Blood Group
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.bloodGroup || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Birth Place
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.birthPlace || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Mother Tongue
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.motherTongue || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Religion
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.religion || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Caste
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.caste || 'Not provided'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Sub Caste
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {profileData.subCaste || 'Not provided'}
          </Text>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaProvider>
        <NavigationHeader title="Profile" onBackPress={() => navigation.goBack()} />
        <SafeAreaView style={[styles.container, { backgroundColor: isDarkMode ? PrimaryColors.LIGHTGRAY : '#F8FAFC' }]}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={PrimaryColors.ORANGE} />
            <Text style={[styles.loadingText, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
              Loading profile...
            </Text>
          </View>
        </SafeAreaView>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <NavigationHeader title="Profile" onBackPress={() => navigation.goBack()} />
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? PrimaryColors.LIGHTGRAY : '#F8FAFC' }
        ]}
        edges={['left', 'right']}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          {/* Profile Header */}
          <View style={[styles.profileHeader, { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }]}>
            {renderAvatar()}
            <Text style={[styles.profileName, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
              {studentData.firstName} {studentData.lastName}
            </Text>
            <Text style={[styles.profileEmail, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
              {studentData.email}
            </Text>
            
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => navigation.navigate('StudentProfile')}
            >
              <Ionicons name="create-outline" size={20} color={PrimaryColors.WHITE} />
              <Text style={styles.editButtonText}>Edit Profile</Text>
            </TouchableOpacity>
          </View>

          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'Personal' && styles.activeTab,
                { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }
              ]}
              onPress={() => setActiveTab('Personal')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'Personal' && styles.activeTabText,
                { color: activeTab === 'Personal' ? PrimaryColors.ORANGE : (isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW) }
              ]}>
                Personal Info
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'Other' && styles.activeTab,
                { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }
              ]}
              onPress={() => setActiveTab('Other')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'Other' && styles.activeTabText,
                { color: activeTab === 'Other' ? PrimaryColors.ORANGE : (isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW) }
              ]}>
                Other Info
              </Text>
            </TouchableOpacity>
          </View>

          {/* Content */}
          {activeTab === 'Personal' ? renderPersonalInfo() : renderOtherInfo()}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  profileHeader: {
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  defaultAvatar: {
    backgroundColor: PrimaryColors.ORANGE,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
  },
  avatarText: {
    color: PrimaryColors.WHITE,
    fontSize: 28,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  profileEmail: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: PrimaryColors.ORANGE,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
  },
  editButtonText: {
    color: PrimaryColors.WHITE,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: PrimaryColors.ORANGE,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
  },
  activeTabText: {
    color: PrimaryColors.ORANGE,
  },
  section: {
    marginBottom: 16,
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  infoGrid: {
    gap: 16,
  },
  infoItem: {
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '400',
    paddingBottom: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
});

export default StudentProfileInfo;
